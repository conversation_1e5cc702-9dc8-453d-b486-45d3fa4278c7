"use client"
import { useEffect } from 'react'

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if performance API is available
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined' || !window.performance) {
      return
    }

    const measurePerformance = () => {
      // Core Web Vitals measurement
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          // Log performance metrics (replace with actual analytics)
          console.log(`Performance metric: ${entry.name}`, entry)
          
          // Store in localStorage for now (replace with actual analytics service)
          const metrics = JSON.parse(localStorage.getItem('vierla-performance') || '[]')
          metrics.push({
            name: entry.name,
            value: entry.value || entry.duration,
            timestamp: Date.now(),
            url: window.location.pathname
          })
          
          // Keep only last 100 metrics
          if (metrics.length > 100) {
            metrics.splice(0, metrics.length - 100)
          }
          
          localStorage.setItem('vierla-performance', JSON.stringify(metrics))
        }
      })

      // Observe Core Web Vitals
      try {
        observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
      } catch (e) {
        // Fallback for browsers that don't support all entry types
        observer.observe({ entryTypes: ['paint', 'navigation'] })
      }

      // Measure custom metrics
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          const metrics = {
            // Page load metrics
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            
            // Network metrics
            dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcpConnect: navigation.connectEnd - navigation.connectStart,
            
            // Time to first byte
            ttfb: navigation.responseStart - navigation.requestStart,
            
            // Resource loading
            resourceLoad: navigation.loadEventEnd - navigation.responseEnd,
            
            // Total page load time
            totalLoadTime: navigation.loadEventEnd - navigation.navigationStart
          }
          
          // Store custom metrics
          const existingMetrics = JSON.parse(localStorage.getItem('vierla-custom-metrics') || '[]')
          existingMetrics.push({
            ...metrics,
            timestamp: Date.now(),
            url: window.location.pathname,
            userAgent: navigator.userAgent,
            connection: (navigator as any).connection?.effectiveType || 'unknown'
          })
          
          // Keep only last 50 custom metrics
          if (existingMetrics.length > 50) {
            existingMetrics.splice(0, existingMetrics.length - 50)
          }
          
          localStorage.setItem('vierla-custom-metrics', JSON.stringify(existingMetrics))
        }
      }, 1000)
    }

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measurePerformance()
    } else {
      window.addEventListener('load', measurePerformance)
    }

    // Cleanup
    return () => {
      window.removeEventListener('load', measurePerformance)
    }
  }, [])

  return null // This component doesn't render anything
}

// Utility function to get performance metrics (for debugging)
export function getPerformanceMetrics() {
  if (typeof window === 'undefined') return null
  
  return {
    coreWebVitals: JSON.parse(localStorage.getItem('vierla-performance') || '[]'),
    customMetrics: JSON.parse(localStorage.getItem('vierla-custom-metrics') || '[]')
  }
}

// Utility function to clear performance data
export function clearPerformanceData() {
  if (typeof window === 'undefined') return
  
  localStorage.removeItem('vierla-performance')
  localStorage.removeItem('vierla-custom-metrics')
}
