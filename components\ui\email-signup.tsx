"use client"
import { useState } from 'react'
import { trackEmailSignup } from '@/components/analytics'
import { submitEmailSignup } from '@/lib/api'

interface EmailSignupProps {
  variant?: 'default' | 'compact'
  className?: string
}

export function EmailSignup({ variant = 'default', className = '' }: EmailSignupProps) {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address')
      setIsSubmitting(false)
      return
    }

    try {
      // Submit to backend API with fallback to localStorage
      const response = await submitEmailSignup(email, variant === 'compact' ? 'hero_section' : 'full_form')

      if (response.success) {
        setIsSubmitted(true)
        setEmail('')

        // Track email signup
        trackEmailSignup(variant === 'compact' ? 'hero_section' : 'full_form')
      } else {
        setError(response.error || 'Something went wrong. Please try again.')
      }
    } catch (err) {
      setError('Something went wrong. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <div className={`text-center ${className}`}>
        <div className="inline-flex items-center px-6 py-3 rounded-full border-2" style={{backgroundColor: 'rgba(244, 241, 232, 0.1)', borderColor: '#F4F1E8'}}>
          <svg className="w-5 h-5 mr-2" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span className="text-white font-medium">Thank you! We'll be in touch soon.</span>
        </div>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <form onSubmit={handleSubmit} className={`flex flex-col sm:flex-row gap-3 max-w-md mx-auto ${className}`}>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
          className="flex-1 px-4 py-3 rounded-full bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
          required
        />
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-3 rounded-full font-medium transition-all duration-300 disabled:opacity-50"
          style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
        >
          {isSubmitting ? 'Joining...' : 'Join Professional Waitlist'}
        </button>
        {error && <p className="text-red-300 text-sm mt-2">{error}</p>}
      </form>
    )
  }

  return (
    <div className={`text-center ${className}`}>
      <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>
        Join Our Professional Waitlist
      </h3>
      <p className="text-white/90 mb-6 max-w-md mx-auto">
        Are you a beauty professional? Be the first to join Vierla when we launch in Toronto & Ottawa. Get exclusive early access.
      </p>
      
      <form onSubmit={handleSubmit} className="max-w-md mx-auto">
        <div className="flex flex-col sm:flex-row gap-3">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
            className="flex-1 px-4 py-3 rounded-full bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
            required
          />
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-3 rounded-full font-medium transition-all duration-300 disabled:opacity-50 hover:scale-105"
            style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
          >
            {isSubmitting ? 'Joining...' : 'Join Professional Waitlist'}
          </button>
        </div>
        {error && <p className="text-red-300 text-sm mt-3">{error}</p>}
      </form>
      
      <p className="text-white/60 text-sm mt-4">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  )
}
