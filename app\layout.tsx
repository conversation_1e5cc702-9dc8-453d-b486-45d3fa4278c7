import type { Metadata } from 'next'
import './globals.css'
import { StructuredData } from '@/components/structured-data'
import { PerformanceMonitor } from '@/components/performance-monitor'
import { Analytics } from '@/components/analytics'
import { PerformanceDashboard } from '@/components/performance-dashboard'
import { BackendStatusIndicator } from '@/components/backend-status'
import { TestingSuite } from '@/components/testing-suite'

export const metadata: Metadata = {
  title: 'Vierla - Your Self-Care, Simplified',
  description: 'Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence. Your place or theirs, your choice. Launching in Toronto & Ottawa.',
  generator: 'Next.js',
  metadataBase: new URL('https://vierla.com'),
  alternates: {
    canonical: '/',
  },
  other: {
    'X-Frame-Options': 'SAMEORIGIN',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  },
  openGraph: {
    title: '<PERSON><PERSON><PERSON> - Your Self-Care, Simplified',
    description: 'Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence. Your place or theirs, your choice. Launching in Toronto & Ottawa.',
    url: 'https://vierla.com',
    siteName: 'Vierla',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Vierla - Expert Beauty Services, Delivered to You',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vierla - Your Self-Care, Simplified',
    description: 'Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence. Your place or theirs, your choice. Launching in Toronto & Ottawa.',
    images: ['/og-image.svg'],
  },
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="theme-color" content="#364035" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
        <style>{`
          @font-face {
            font-family: 'Playfair Display';
            font-display: swap;
          }
          @font-face {
            font-family: 'Manrope';
            font-display: swap;
          }
        `}</style>
      </head>
      <body
        style={{
          fontFamily: 'Manrope, sans-serif',
          background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)',
          backgroundAttachment: 'fixed',
          minHeight: '100vh',
          minHeight: '-webkit-fill-available',
          width: '100%',
          overflowX: 'hidden',
          margin: 0,
          padding: 0
        }}
        className="mobile-full-height"
      >
        <StructuredData />
        <PerformanceMonitor />
        <Analytics />
        <PerformanceDashboard />
        <BackendStatusIndicator />
        <TestingSuite />
        {children}
      </body>
    </html>
  )
}
