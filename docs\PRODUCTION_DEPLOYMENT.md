# 🚀 Production Deployment Guide - Vierla Platform

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Target Environment**: Production  

---

## 📋 **Quick Start Deployment**

### **Prerequisites**
- Ubuntu 22.04 LTS server with 8GB+ RAM
- Domain: vierla.com pointing to server IP
- Docker and Docker Compose installed
- SSL certificate capability (Let's Encrypt)

### **Rapid Deployment Steps**
```bash
# 1. Clone repository
git clone https://github.com/your-org/vierla-web.git /opt/vierla
cd /opt/vierla

# 2. Configure environment
cp .env.example .env.production
nano .env.production  # Edit with production values

# 3. Deploy with Docker
docker-compose -f docker-compose.prod.yml up -d

# 4. Configure Nginx (see detailed steps below)
# 5. Obtain SSL certificate
sudo certbot --nginx -d vierla.com

# 6. Verify deployment
curl -f https://vierla.com/api/health
```

---

## 🗄️ **Database Setup**

### **PostgreSQL with Docker**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: vierla
      POSTGRES_USER: vierla_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - vierla-network

volumes:
  postgres_data:
networks:
  vierla-network:
```

### **Database Schema**
```sql
-- init.sql
CREATE TABLE email_signups (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    source VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active'
);

CREATE TABLE contact_forms (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE professional_applications (
    id SERIAL PRIMARY KEY,
    application_id VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    services JSONB NOT NULL,
    experience VARCHAR(50) NOT NULL,
    license_number VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending_review',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🚀 **Application Deployment**

### **Dockerfile**
```dockerfile
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
USER nextjs
EXPOSE 3000
ENV PORT 3000
CMD ["node", "server.js"]
```

### **Docker Compose Production**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://vierla_user:${DB_PASSWORD}@db:5432/vierla
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - FROM_EMAIL=<EMAIL>
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - vierla-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: vierla
      POSTGRES_USER: vierla_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - vierla-network

volumes:
  postgres_data:

networks:
  vierla-network:
    driver: bridge
```

---

## 🌐 **Nginx Configuration**

### **Site Configuration**
```nginx
# /etc/nginx/sites-available/vierla.com
server {
    listen 80;
    server_name vierla.com www.vierla.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name vierla.com www.vierla.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/vierla.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/vierla.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;

    # Main application
    location / {
        limit_req zone=general burst=5 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API endpoints with stricter rate limiting
    location /api/ {
        limit_req zone=api burst=3 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static assets with caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # Health check (no rate limiting)
    location /api/health {
        proxy_pass http://localhost:3000;
        access_log off;
    }
}
```

---

## 🔒 **Security Configuration**

### **Environment Variables**
```bash
# .env.production
NODE_ENV=production
PORT=3000

# Database
DB_PASSWORD=your_secure_database_password
DATABASE_URL=postgresql://vierla_user:${DB_PASSWORD}@db:5432/vierla

# Email Service (SendGrid example)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_FB_PIXEL_ID=000000000000000
```

### **Firewall Setup**
```bash
# UFW Configuration
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw --force enable

# Fail2ban for additional protection
sudo apt install fail2ban
sudo systemctl enable fail2ban
```

---

## 📊 **Monitoring & Logging**

### **Health Monitoring Script**
```bash
#!/bin/bash
# /opt/health-check.sh

HEALTH_URL="https://vierla.com/api/health"
LOG_FILE="/var/log/vierla-health.log"

response=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $response -eq 200 ]; then
    echo "$(date): Health check passed" >> $LOG_FILE
else
    echo "$(date): Health check failed with code $response" >> $LOG_FILE
    # Send alert (email, Slack, etc.)
fi
```

### **Log Rotation**
```bash
# /etc/logrotate.d/vierla
/var/log/vierla-*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
```

---

## 🔄 **Backup Strategy**

### **Automated Backup Script**
```bash
#!/bin/bash
# /opt/backup-vierla.sh

BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Database backup
docker exec vierla_db_1 pg_dump -U vierla_user vierla > $BACKUP_DIR/db_$DATE.sql
gzip $BACKUP_DIR/db_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/vierla

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### **Cron Schedule**
```bash
# Daily backup at 2 AM
0 2 * * * /opt/backup-vierla.sh

# Health check every 5 minutes
*/5 * * * * /opt/health-check.sh
```

---

## ✅ **Deployment Verification**

### **Automated Testing Script**
```bash
#!/bin/bash
# /opt/verify-deployment.sh

echo "Starting deployment verification..."

# Test health endpoint
echo "Testing health endpoint..."
if curl -f https://vierla.com/api/health; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test main page
echo "Testing main page..."
if curl -f https://vierla.com | grep -q "Vierla"; then
    echo "✅ Main page loaded"
else
    echo "❌ Main page failed"
    exit 1
fi

# Test SSL
echo "Testing SSL certificate..."
if curl -I https://vierla.com | grep -q "200 OK"; then
    echo "✅ SSL working"
else
    echo "❌ SSL failed"
    exit 1
fi

echo "✅ All verification tests passed!"
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Container Won't Start**
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs app

# Check container status
docker-compose -f docker-compose.prod.yml ps

# Rebuild if needed
docker-compose -f docker-compose.prod.yml build --no-cache
```

#### **Database Connection Issues**
```bash
# Test database connection
docker exec -it vierla_db_1 psql -U vierla_user -d vierla

# Check database logs
docker-compose -f docker-compose.prod.yml logs db
```

#### **SSL Certificate Issues**
```bash
# Renew certificate
sudo certbot renew

# Test certificate
sudo certbot certificates

# Check Nginx configuration
sudo nginx -t
```

---

## 📞 **Support & Maintenance**

### **Regular Maintenance Tasks**
- **Daily**: Check health status and logs
- **Weekly**: Review performance metrics
- **Monthly**: Update security patches
- **Quarterly**: Review and update documentation

### **Emergency Contacts**
- **Technical Support**: <EMAIL>
- **Infrastructure Issues**: Follow escalation procedures

---

**This production deployment guide ensures a secure, scalable, and maintainable deployment of the Vierla platform.**
