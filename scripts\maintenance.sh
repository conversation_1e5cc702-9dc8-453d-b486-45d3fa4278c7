#!/bin/bash

# Vierla Maintenance Mode Management Script
# This script manages maintenance mode for the Vierla website

# Configuration
SITE_ROOT="/var/www/vierla.com"
MAINTENANCE_FLAG="$SITE_ROOT/maintenance.flag"
NGINX_CONFIG="/etc/nginx/sites-available/vierla.com"
LOG_FILE="/var/log/vierla-maintenance.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo -e "$1"
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}Error: This script must be run as root or with sudo${NC}"
        exit 1
    fi
}

# Check if NGINX is running
check_nginx() {
    if ! systemctl is-active --quiet nginx; then
        echo -e "${RED}Warning: NGINX is not running${NC}"
        return 1
    fi
    return 0
}

# Enable maintenance mode
enable_maintenance() {
    log_message "${BLUE}Enabling maintenance mode for Vierla...${NC}"
    
    # Create maintenance flag
    touch "$MAINTENANCE_FLAG"
    if [[ $? -eq 0 ]]; then
        log_message "${GREEN}✓ Maintenance flag created${NC}"
    else
        log_message "${RED}✗ Failed to create maintenance flag${NC}"
        exit 1
    fi
    
    # Reload NGINX configuration
    if check_nginx; then
        nginx -t && systemctl reload nginx
        if [[ $? -eq 0 ]]; then
            log_message "${GREEN}✓ NGINX configuration reloaded${NC}"
        else
            log_message "${RED}✗ Failed to reload NGINX configuration${NC}"
            exit 1
        fi
    fi
    
    log_message "${GREEN}✓ Maintenance mode enabled successfully${NC}"
    log_message "${YELLOW}Website is now showing maintenance page${NC}"
}

# Disable maintenance mode
disable_maintenance() {
    log_message "${BLUE}Disabling maintenance mode for Vierla...${NC}"
    
    # Remove maintenance flag
    if [[ -f "$MAINTENANCE_FLAG" ]]; then
        rm "$MAINTENANCE_FLAG"
        if [[ $? -eq 0 ]]; then
            log_message "${GREEN}✓ Maintenance flag removed${NC}"
        else
            log_message "${RED}✗ Failed to remove maintenance flag${NC}"
            exit 1
        fi
    else
        log_message "${YELLOW}Maintenance flag not found - maintenance mode may already be disabled${NC}"
    fi
    
    # Reload NGINX configuration
    if check_nginx; then
        nginx -t && systemctl reload nginx
        if [[ $? -eq 0 ]]; then
            log_message "${GREEN}✓ NGINX configuration reloaded${NC}"
        else
            log_message "${RED}✗ Failed to reload NGINX configuration${NC}"
            exit 1
        fi
    fi
    
    log_message "${GREEN}✓ Maintenance mode disabled successfully${NC}"
    log_message "${YELLOW}Website is now serving normal content${NC}"
}

# Check maintenance status
check_status() {
    echo -e "${BLUE}Checking Vierla maintenance status...${NC}"
    
    if [[ -f "$MAINTENANCE_FLAG" ]]; then
        echo -e "${YELLOW}Status: MAINTENANCE MODE ENABLED${NC}"
        echo -e "Flag file: $MAINTENANCE_FLAG"
        echo -e "Created: $(stat -c %y "$MAINTENANCE_FLAG")"
    else
        echo -e "${GREEN}Status: NORMAL OPERATION${NC}"
        echo -e "Maintenance mode is disabled"
    fi
    
    # Check NGINX status
    if check_nginx; then
        echo -e "${GREEN}NGINX: Running${NC}"
    else
        echo -e "${RED}NGINX: Not running${NC}"
    fi
    
    # Test website response
    echo -e "\n${BLUE}Testing website response...${NC}"
    response=$(curl -s -o /dev/null -w "%{http_code}" https://vierla.com)
    case $response in
        200)
            echo -e "${GREEN}Website: Online (HTTP $response)${NC}"
            ;;
        503)
            echo -e "${YELLOW}Website: Maintenance Mode (HTTP $response)${NC}"
            ;;
        *)
            echo -e "${RED}Website: Error (HTTP $response)${NC}"
            ;;
    esac
}

# Schedule maintenance mode
schedule_maintenance() {
    local duration=$1
    if [[ -z "$duration" ]]; then
        echo -e "${RED}Error: Duration not specified${NC}"
        echo "Usage: $0 schedule <duration_in_minutes>"
        exit 1
    fi
    
    log_message "${BLUE}Scheduling maintenance mode for $duration minutes...${NC}"
    
    # Enable maintenance mode
    enable_maintenance
    
    # Schedule disable maintenance mode
    echo "sleep $((duration * 60)) && $0 disable" | at now
    if [[ $? -eq 0 ]]; then
        log_message "${GREEN}✓ Maintenance mode scheduled to disable in $duration minutes${NC}"
    else
        log_message "${RED}✗ Failed to schedule maintenance disable${NC}"
        log_message "${YELLOW}You will need to manually disable maintenance mode${NC}"
    fi
}

# Show usage information
show_usage() {
    echo -e "${BLUE}Vierla Maintenance Mode Management${NC}"
    echo ""
    echo "Usage: $0 {enable|disable|status|schedule}"
    echo ""
    echo "Commands:"
    echo "  enable              Enable maintenance mode"
    echo "  disable             Disable maintenance mode"
    echo "  status              Check current maintenance status"
    echo "  schedule <minutes>  Enable maintenance mode for specified duration"
    echo ""
    echo "Examples:"
    echo "  $0 enable                    # Enable maintenance mode"
    echo "  $0 disable                   # Disable maintenance mode"
    echo "  $0 status                    # Check status"
    echo "  $0 schedule 30               # Enable for 30 minutes"
    echo ""
    echo "Log file: $LOG_FILE"
}

# Main script logic
main() {
    check_permissions
    
    case "$1" in
        enable)
            enable_maintenance
            ;;
        disable)
            disable_maintenance
            ;;
        status)
            check_status
            ;;
        schedule)
            schedule_maintenance "$2"
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
