# Vierla - Your Self-Care, Simplified

A comprehensive pre-launch platform for beauty service providers and customers. Vierla will connect beauty service providers with customers, enabling providers to create digital stores, manage bookings, access analytics, and process payments while giving customers a centralized location to discover, book, and pay for beauty and self-care services.

## Current Status: Pre-Launch

Vierla is currently in pre-launch phase with:
- **Professional applications opening soon** for service providers
- **Dark Sage Green design** conveying trust and sophistication
- **Coming soon messaging** for iOS and Android apps
- **Provider registration interest** collection

## Planned Features (Coming Soon)

### For Service Providers
- **Digital Stores**: Create and customize your own online presence
- **Booking Management**: Manage appointments and schedules
- **Business Analytics**: Track performance and customer insights
- **Payment Processing**: Secure payment handling
- **Portfolio Showcase**: Display your work and services

### For Customers
- **Service Discovery**: Find beauty and self-care services in your area
- **Easy Booking**: Book appointments with your preferred providers
- **Secure Messaging**: Communicate directly with service providers
- **Secure Payments**: Safe and convenient payment processing
- **Service Reviews**: View and leave reviews for services

## Services to be Offered

- **Barbers**: Classic cuts, beard trims, hot towel shaves, hair styling
- **Makeup**: Event makeup, bridal looks, fashion makeup, everyday glam
- **Salons**: Hair cuts & color, blowouts, treatments, full styling
- **Locs**: Loc maintenance, retwisting, loc styling, loc repair
- **Braids**: Box braids, cornrows, French braids, protective styles
- **Nails**: Manicures, pedicures, nail art, gel polish
- **Brows**: Eyebrow shaping, threading, tinting, microblading
- **Eyelashes**: Lash extensions, lash lifts, lash tinting, volume lashes

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI components
- **Icons**: Lucide React
- **Build Tool**: Next.js with TypeScript
- **Package Manager**: npm/pnpm
- **Deployment**: Docker, Docker Compose, Nginx
- **Development**: ESLint, PostCSS, shadcn/ui

## Getting Started

### Prerequisites
- Node.js 18+
- npm or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://gitlab.ameerosman.com/icecrown/services-application/services-app-web.git
cd services-app-web
```

2. Install dependencies:
```bash
npm install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Build for Production

```bash
npm run build
npm start
```

## Project Structure

```
vierla/
├── app/                    # Next.js App Router pages
├── components/             # Reusable React components
│   └── ui/                # shadcn/ui component library
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
├── public/                # Static assets
├── docs/                  # Documentation
├── config/                # Configuration files
├── docker-compose.yml     # Docker configuration
├── Dockerfile             # Docker build
└── deploy.sh              # Deployment script
```

See [docs/PROJECT_STRUCTURE.md](./docs/PROJECT_STRUCTURE.md) for detailed structure documentation.

## Deployment

See [docs/DEPLOYMENT.md](./docs/DEPLOYMENT.md) for detailed instructions on deploying to a private Debian server with Docker and NGINX reverse proxy.

## Documentation

- **[Project Structure](./docs/PROJECT_STRUCTURE.md)** - Detailed project organization
- **[Deployment Guide](./docs/DEPLOYMENT.md)** - Complete deployment instructions
- **[Project Status](./docs/PROJECT_STATUS.md)** - Current status and features
- **[Changes Summary](./docs/CHANGES_SUMMARY.md)** - Detailed change log

## Contributing

We welcome contributions! Please feel free to submit a Pull Request.

## License

This project is proprietary software. All rights reserved.

## Support

For support, please contact the development team or create an issue in the project repository.
