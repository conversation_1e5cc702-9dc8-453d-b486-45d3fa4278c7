"use client"
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { Footer } from "@/components/ui/footer"

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with Sophisticated Earth Tones */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(54, 64, 53, 0.4), rgba(139, 154, 140, 0.3), rgba(54, 64, 53, 0.4))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
          </Link>
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
            <Link href="/about" className="text-white/90 hover:text-white transition-colors">About</Link>
          </div>
        </nav>
      </header>

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-8" style={{fontFamily: 'Playfair Display, serif'}}>
              Privacy Policy
            </h1>
            
            <div className="prose prose-lg prose-invert max-w-none">
              <p className="text-white/90 text-lg mb-6">
                <strong>Effective Date:</strong> January 2025
              </p>
              
              <p className="text-white/90 mb-6">
                At Vierla ("we," "our," or "us"), we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website www.vierla.com and use our services.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Information We Collect</h2>
              
              <h3 className="text-xl font-semibold text-white mt-6 mb-3">Personal Information</h3>
              <p className="text-white/90 mb-4">
                We may collect personal information that you voluntarily provide to us, including:
              </p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Name and contact information (email address, phone number, address)</li>
                <li>• Account credentials and profile information</li>
                <li>• Payment and billing information</li>
                <li>• Service preferences and booking history</li>
                <li>• Communications with us</li>
              </ul>

              <h3 className="text-xl font-semibold text-white mt-6 mb-3">Automatically Collected Information</h3>
              <p className="text-white/90 mb-4">
                When you visit our website, we may automatically collect:
              </p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Device information (IP address, browser type, operating system)</li>
                <li>• Usage data (pages visited, time spent, click patterns)</li>
                <li>• Location information (with your consent)</li>
                <li>• Cookies and similar tracking technologies</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">How We Use Your Information</h2>
              <p className="text-white/90 mb-4">We use your information to:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Provide and improve our beauty services platform</li>
                <li>• Process bookings and payments</li>
                <li>• Communicate with you about services and updates</li>
                <li>• Personalize your experience</li>
                <li>• Ensure platform security and prevent fraud</li>
                <li>• Comply with legal obligations</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Information Sharing</h2>
              <p className="text-white/90 mb-4">
                We do not sell your personal information. We may share your information with:
              </p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Beauty professionals to fulfill service bookings</li>
                <li>• Service providers who assist our operations</li>
                <li>• Legal authorities when required by law</li>
                <li>• Business partners with your consent</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Data Security</h2>
              <p className="text-white/90 mb-6">
                We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no internet transmission is completely secure.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Your Rights</h2>
              <p className="text-white/90 mb-4">You have the right to:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Access and update your personal information</li>
                <li>• Request deletion of your data</li>
                <li>• Opt-out of marketing communications</li>
                <li>• Request data portability</li>
                <li>• Lodge complaints with supervisory authorities</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Cookies</h2>
              <p className="text-white/90 mb-6">
                We use cookies and similar technologies to enhance your browsing experience, analyze website traffic, and personalize content. You can control cookie preferences through your browser settings.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Contact Us</h2>
              <p className="text-white/90 mb-6">
                If you have questions about this Privacy Policy or our data practices, please contact us at:
                <br />
                Email: <EMAIL>
              </p>

              <p className="text-white/90 text-sm mt-8">
                This Privacy Policy may be updated periodically. We will notify you of significant changes through our website or email.
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
