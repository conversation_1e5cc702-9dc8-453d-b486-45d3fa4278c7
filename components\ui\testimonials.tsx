"use client"
import { useState, useEffect } from 'react'

interface Testimonial {
  id: string
  name: string
  role: string
  location: string
  content: string
  rating: number
  service: string
  image?: string
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Marketing Executive',
    location: 'New York, NY',
    content: '<PERSON><PERSON><PERSON> transformed my morning routine. Having a professional stylist come to my home before important meetings is a game-changer. The quality and convenience are unmatched.',
    rating: 5,
    service: 'Hair Styling',
    image: '/testimonials/sarah.jpg'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: '<PERSON>',
    location: 'Los Angeles, CA',
    content: 'For my wedding day, I needed perfection. The makeup artist from <PERSON><PERSON><PERSON> exceeded all expectations. Professional, punctual, and absolutely talented. My day was stress-free.',
    rating: 5,
    service: 'Bridal Makeup',
    image: '/testimonials/maria.jpg'
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Entrepreneur',
    location: 'San Francisco, CA',
    content: 'As a busy entrepreneur, time is everything. <PERSON><PERSON><PERSON> brings the salon to me. The nail technician was skilled, professional, and my nails looked amazing for weeks.',
    rating: 5,
    service: 'Nail Services',
    image: '/testimonials/jennifer.jpg'
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'New Mom',
    location: 'Chicago, IL',
    content: 'After having my baby, self-care became challenging. Vierla made it possible again. Professional services at home while my little one napped. Absolutely perfect.',
    rating: 5,
    service: 'Hair & Makeup',
    image: '/testimonials/ashley.jpg'
  }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <section className="relative z-10 py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6" style={{fontFamily: 'Playfair Display, serif'}}>
            What Our Customers Say
          </h2>
          <p className="text-xl text-white/90 max-w-2xl mx-auto">
            Real experiences from real customers who trust Vierla for their beauty needs.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20">
            <div className="text-center">
              {/* Stars */}
              <div className="flex justify-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className="w-6 h-6 mx-1"
                    style={{color: '#B8956A'}}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>

              {/* Testimonial Content */}
              <blockquote className="text-xl md:text-2xl text-white/95 mb-8 leading-relaxed italic">
                "{currentTestimonial.content}"
              </blockquote>

              {/* Customer Info */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 rounded-full mb-4 flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                  <span className="text-2xl font-bold text-white">
                    {currentTestimonial.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <h4 className="text-lg font-semibold text-white mb-1">
                  {currentTestimonial.name}
                </h4>
                <p className="text-white/80 mb-1">
                  {currentTestimonial.role}
                </p>
                <p className="text-white/60 text-sm mb-2">
                  {currentTestimonial.location}
                </p>
                <span className="inline-block px-3 py-1 rounded-full text-sm" style={{backgroundColor: 'rgba(184, 149, 106, 0.2)', color: '#B8956A'}}>
                  {currentTestimonial.service}
                </span>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8">
              <button
                onClick={prevTestimonial}
                className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              {/* Dots */}
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex ? 'bg-white' : 'bg-white/30'
                    }`}
                  />
                ))}
              </div>

              <button
                onClick={nextTestimonial}
                className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export function TrustSignals() {
  const trustMetrics = [
    { label: 'Verified Professionals', value: '500+', icon: 'shield' },
    { label: 'Services Completed', value: '10,000+', icon: 'check' },
    { label: 'Customer Satisfaction', value: '99%', icon: 'heart' },
    { label: 'Cities Served', value: '25+', icon: 'location' }
  ]

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'shield':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        )
      case 'check':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      case 'heart':
        return (
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
        )
      case 'location':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <section className="relative z-10 py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {trustMetrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.1)', border: '1px solid rgba(244, 241, 232, 0.2)'}}>
                <div style={{color: '#F4F1E8'}}>
                  {getIcon(metric.icon)}
                </div>
              </div>
              <div className="text-3xl font-bold text-white mb-2" style={{fontFamily: 'Playfair Display, serif'}}>
                {metric.value}
              </div>
              <div className="text-white/80 text-sm">
                {metric.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
