<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Page Not Found - Vierla</title>
    <meta name="theme-color" content="#364035">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        /* Reset and base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
            background-attachment: fixed;
            color: white;
            overflow-x: hidden;
        }

        /* Mobile fixes */
        @supports (-webkit-touch-callout: none) {
            html::before {
                content: '';
                position: fixed;
                top: -100px;
                left: -100px;
                right: -100px;
                bottom: -100px;
                background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
                z-index: -10;
                pointer-events: none;
            }
        }

        .container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px 32px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: rgba(244, 241, 232, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            border: 2px solid rgba(244, 241, 232, 0.3);
        }

        .logo-text {
            font-size: 36px;
            font-weight: bold;
            color: #F4F1E8;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #B8956A;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 16px;
            font-family: 'Georgia', serif;
        }

        h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 32px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 32px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: #B8956A;
            color: #2D2A26;
        }

        .btn-primary:hover {
            background: #A67C52;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #F4F1E8;
            border-color: rgba(244, 241, 232, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(244, 241, 232, 0.1);
            transform: translateY(-2px);
        }

        .contact-info {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .contact-info a {
            color: #B8956A;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-info a:hover {
            color: #F4F1E8;
            transition: color 0.3s ease;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .error-card {
                padding: 32px 24px;
                margin: 16px;
            }

            .error-code {
                font-size: 80px;
            }

            h1 {
                font-size: 36px;
            }

            .logo-text {
                font-size: 28px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
                justify-content: center;
            }
        }

        /* Landscape orientation fix */
        @media screen and (orientation: landscape) and (max-height: 500px) {
            .container {
                padding: 10px;
            }
            
            .error-card {
                padding: 24px 20px;
            }
            
            .error-code {
                font-size: 60px;
                margin-bottom: 8px;
            }
            
            h1 {
                font-size: 28px;
                margin-bottom: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-card">
            <!-- Logo -->
            <div class="logo">
                <div class="logo-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F4F1E8" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                </div>
                <span class="logo-text">Vierla</span>
            </div>

            <!-- Error Code -->
            <div class="error-code">404</div>

            <!-- Content -->
            <h1>Page Not Found</h1>
            
            <p class="description">
                The page you're looking for doesn't exist or has been moved. 
                Don't worry, you can still explore our beauty services platform and find what you need.
            </p>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="/" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                    </svg>
                    Go Home
                </a>
                <a href="/about" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                    Learn More
                </a>
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <p>Need help finding something?</p>
                <p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p style="margin-top: 16px; font-size: 14px;">
                    &copy; 2025 Vierla - Your Self-Care, Simplified. All rights reserved.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
