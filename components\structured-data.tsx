import Script from 'next/script'

export function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<PERSON>ier<PERSON>",
    "description": "Expert beauty services delivered to you. Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence.",
    "url": "https://www.vierla.com",
    "logo": "https://www.vierla.com/favicon.svg",
    "sameAs": [
      // Add social media URLs when available
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "areaServed": {
      "@type": "Country",
      "name": "United States"
    },
    "serviceType": [
      "Hair Styling",
      "Makeup Services",
      "Nail Services",
      "Beauty Services",
      "Mobile Beauty Services"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Beauty Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Hair Styling Services",
            "description": "Professional hair cuts, styling, and treatments delivered to your location"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Makeup Services",
            "description": "Professional makeup application for events, bridal, and everyday looks"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Nail Services",
            "description": "Manicures, pedicures, and nail art services at your location"
          }
        }
      ]
    }
  }

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  )
}
