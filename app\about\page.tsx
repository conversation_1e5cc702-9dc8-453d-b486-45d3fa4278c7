"use client"
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, ArrowLeft, Target, Eye, Clock } from "lucide-react"
import <PERSON> from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/ui/footer"

export default function AboutPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with Sophisticated Earth Tones */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(54, 64, 53, 0.4), rgba(139, 154, 140, 0.3), rgba(54, 64, 53, 0.4))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
          </Link>
          <div className="flex items-center space-x-8">
            <Link
              href="/"
              className="flex items-center text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Home
            </Link>

          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h1 className="text-5xl md:text-7xl font-black mb-8 leading-none text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>
            <span className="inline-block">About</span>
            <br />
            <span className="inline-block" style={{color: '#F4F1E8'}}>
              Vierla
            </span>
          </h1>
          <p className="text-xl text-white/90 leading-relaxed font-light drop-shadow-sm max-w-4xl mx-auto">
            Building the future of beauty services through trust, quality, and convenience. We&apos;re creating a curated marketplace that connects discerning customers with top-tier beauty professionals. Launching first in Toronto & Ottawa.
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Our Story Section */}
            <div className="mb-20">
              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30 max-w-4xl mx-auto text-center">
                <h2 className="text-5xl font-bold text-white mb-8 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>Our Story</h2>
                <p className="text-white/90 text-lg leading-relaxed mb-6 drop-shadow-sm">
                  Founded in 2025, Vierla emerged from a vision to revolutionize how beauty service providers connect with customers.
                  We recognized the need for a comprehensive platform that empowers providers while simplifying discovery and booking for customers.
                </p>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  We&apos;re building an ecosystem that benefits both sides of the beauty industry - helping providers grow their businesses
                  while giving customers easy access to quality beauty and self-care services in their area.
                </p>
              </div>
            </div>

            {/* Dual Audience Cards */}
            <div className="grid lg:grid-cols-2 gap-12 mb-20">
              {/* For Service Providers */}
              <div className="bg-white/15 backdrop-blur-md rounded-3xl p-10 shadow-2xl border border-white/25 transform hover:scale-105 transition-all duration-300">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center" style={{backgroundColor: 'rgba(184, 149, 106, 0.2)'}}>
                    <svg className="w-8 h-8" style={{color: '#B8956A'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>For Service Providers</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed mb-6 drop-shadow-sm">
                  Our platform gives service providers their own digital stores where they can showcase their work, manage bookings,
                  and access business analytics.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#B8956A'}}></span>
                    Digital store creation
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#B8956A'}}></span>
                    Booking management
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#B8956A'}}></span>
                    Business analytics
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#B8956A'}}></span>
                    Payment processing
                  </div>
                </div>
              </div>

              {/* For Customers */}
              <div className="bg-white/15 backdrop-blur-md rounded-3xl p-10 shadow-2xl border border-white/25 transform hover:scale-105 transition-all duration-300">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
                    <svg className="w-8 h-8" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>For Customers</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed mb-6 drop-shadow-sm">
                  We provide a centralized location to discover beauty and self-care services with confidence and convenience.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#F4F1E8'}}></span>
                    Discover services
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#F4F1E8'}}></span>
                    View portfolios
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#F4F1E8'}}></span>
                    Book appointments
                  </div>
                  <div className="flex items-center text-white/80">
                    <span className="w-2 h-2 rounded-full mr-3" style={{backgroundColor: '#F4F1E8'}}></span>
                    Secure payments
                  </div>
                </div>
              </div>
            </div>

            {/* Values Section - Moved Below */}
            <div className="mb-20">
              <h2 className="text-4xl font-bold text-white text-center mb-12 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>Our Values</h2>
              <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                {[
                  {
                    icon: Award,
                    title: "Excellence First",
                    description:
                      "Every professional in our network is certified, background-checked, and committed to delivering exceptional results.",
                  },
                  {
                    icon: Shield,
                    title: "Trust & Safety",
                    description:
                      "Your safety is our priority. All our professionals are insured and follow strict health and safety protocols.",
                  },
                  {
                    icon: Zap,
                    title: "Innovation Driven",
                    description:
                      "We leverage cutting-edge technology to provide seamless booking, real-time tracking, and personalized experiences.",
                  },
                  {
                    icon: Heart,
                    title: "Customer Centric",
                    description:
                      "Every decision we make is guided by our commitment to enhancing your beauty and wellness journey.",
                  },
                ].map((value, index) => (
                  <div
                    key={index}
                    className="bg-white/10 backdrop-blur-md rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-105 transition-all duration-300"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/30">
                        <value.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-white mb-2 drop-shadow-sm">{value.title}</h4>
                        <p className="text-white/80 leading-relaxed drop-shadow-sm">{value.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Vision & Mission Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-6xl font-black text-white mb-6 drop-shadow-lg">Vision & Mission</h2>
              <p className="text-2xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
                Guiding principles that shape our commitment to beauty and wellness
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 mb-20">
              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mr-6 border border-white/30">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-4xl font-bold text-white drop-shadow-sm">Our Vision</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  To become the world&apos;s leading marketplace connecting beauty service providers and customers, empowering providers
                  with comprehensive business tools while giving customers seamless access to quality beauty and self-care services.
                </p>
              </div>

              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mr-6 border border-white/30">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-4xl font-bold text-white drop-shadow-sm">Our Mission</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  To revolutionize the beauty industry by providing service providers with powerful business management tools
                  including digital stores, booking systems, analytics, and payment processing, while offering customers a
                  centralized platform to discover, book, and pay for beauty and self-care services.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Why Choose Vierla?</h2>
              <p className="text-xl text-white/90 drop-shadow-sm">What sets us apart in the beauty industry</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Clock,
                  title: "Convenience",
                  description:
                    "Book services at your preferred time and location. No more waiting in salons or traveling across town.",
                },
                {
                  icon: Shield,
                  title: "Quality Assurance",
                  description:
                    "All professionals are vetted, certified, and regularly reviewed to maintain our high standards.",
                },
                {
                  icon: Heart,
                  title: "Personalized Care",
                  description:
                    "Each service is tailored to your unique needs and preferences for the best possible results.",
                },
              ].map((feature, index) => (
                <Card
                  key={index}
                  className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 rounded-2xl overflow-hidden group cursor-pointer transform hover:scale-105"
                >
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg border border-white/30">
                      <feature.icon className="w-8 h-8 text-white group-hover:animate-bounce transition-all duration-300 drop-shadow-sm" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">
                      {feature.title}
                    </h3>
                    <p className="text-white/80 leading-relaxed group-hover:text-white transition-colors duration-300 drop-shadow-sm">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6" style={{fontFamily: 'Playfair Display, serif'}}>
                Ready to Join Vierla?
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                Take the next step in your beauty career. Join our platform and connect with customers who value quality and professionalism.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/apply"
                  className="inline-flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105"
                  style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                >
                  Apply Now
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center px-8 py-4 rounded-full border-2 text-white hover:bg-white/10 transition-all duration-300 text-lg"
                  style={{borderColor: '#F4F1E8', color: '#F4F1E8'}}
                >
                  Have Questions?
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                  </svg>
                </Link>
              </div>

              <p className="text-white/60 text-sm mt-6">
                Applications are reviewed within 3-5 business days
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>

    </div>
  )
}
