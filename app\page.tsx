"use client"
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"

// import { TestimonialsSection, TrustSignals } from "@/components/ui/testimonials" // Temporarily commented for future use
// import { ProfessionalCTAButton, ABTestResults } from "@/components/ab-testing" // Temporarily commented
import { ABTestResults } from "@/components/ab-testing"
import { Card, CardContent } from "@/components/ui/card"
import { Footer } from "@/components/ui/footer"

export default function HomePage() {
  return <GlassmorphismVariation />
}

// Glassmorphism Variation with Sage Green Theme
function GlassmorphismVariation() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with Sophisticated Earth Tones */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(54, 64, 53, 0.4), rgba(139, 154, 140, 0.3), rgba(54, 64, 53, 0.4))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <div>
              <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
            </div>
          </Link>
          <div className="hidden md:flex items-center space-x-8">
            <span className="text-white/60 font-medium drop-shadow-sm cursor-not-allowed">
              Services
            </span>
            <Link
              href="/about"
              className="transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
              style={{color: '#F5F0E1'}}
              onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#FF6F61'}
              onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F5F0E1'}
            >
              About
            </Link>
            <Link
              href="/contact"
              className="transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
              style={{color: '#F5F0E1'}}
              onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#B8956A'}
              onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F5F0E1'}
            >
              Contact
            </Link>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center max-w-6xl mx-auto">
          <div className="inline-flex items-center rounded-full px-6 py-3 mb-8 shadow-lg border-2 animate-bounce" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', borderColor: '#F4F1E8'}}>
            <span className="text-white font-medium drop-shadow-sm">Launching Soon in Toronto & Ottawa</span>
          </div>

          <h1 className="text-6xl md:text-8xl font-black mb-8 leading-none text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>
            <span className="inline-block" style={{color: '#F4F1E8'}}>
              Your Self-Care, Simplified
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm">
            Your destination for beauty and wellness. The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.
          </p>

          {/* Dual Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Link
              href="/customer-app"
              className="group flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center"
              style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
            >
              <svg className="mr-3 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Find Your Perfect Stylist
              <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>

            <Link
              href="/provider-app"
              className="group flex items-center px-8 py-4 rounded-full border-2 text-white hover:bg-white/10 transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center"
              style={{borderColor: '#F4F1E8', color: '#F4F1E8'}}
            >
              <svg className="mr-3 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
              </svg>
              Grow Your Business
              <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>



          {/* Dual-sided Navigation - Temporarily commented out */}
          {/* <div className="text-center">
            <p className="text-white/80 mb-4">Are you a beauty professional?</p>
            <Link href="/apply">
              <ProfessionalCTAButton />
            </Link>
          </div> */}




        </div>
      </section>

      {/* Trust Bar */}
      <section className="relative z-10 py-8 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-white/80 text-lg mb-4">Trusted by beauty professionals and customers nationwide</p>
            <div className="flex flex-wrap justify-center items-center gap-8 text-white/60">
              <span className="text-sm">✓ Verified Professionals</span>
              <span className="text-sm">✓ Secure Payments</span>
              <span className="text-sm">✓ Satisfaction Guaranteed</span>
              <span className="text-sm">✓ Licensed & Insured</span>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>How It Works</h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
              Getting beautiful has never been this simple
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                <svg className="w-10 h-10" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>1. Discover</h3>
              <p className="text-white/80 leading-relaxed">
                Explore our curated network of top-rated beauty professionals in your area.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                <svg className="w-10 h-10" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>2. Book</h3>
              <p className="text-white/80 leading-relaxed">
                Select your service, choose a time that works for you, and pay securely online.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                <svg className="w-10 h-10" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>3. Relax</h3>
              <p className="text-white/80 leading-relaxed">
                Your vetted professional comes to you, ready to provide an exceptional service.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Signals - Temporarily removed for future use */}
      {/* <TrustSignals /> */}

      {/* Testimonials - Temporarily removed for future use */}
      {/* <TestimonialsSection /> */}

      {/* Services Section - 8 Categories */}
      <section id="services" className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Featured Services</h2>
            <p className="text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-6">
              Discover our curated selection of premium beauty services. Choose to have professionals come to you, or visit their studios.
            </p>
            <div className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white/90" style={{borderColor: '#F4F1E8', backgroundColor: 'rgba(244, 241, 232, 0.1)'}}>
              <span className="text-sm font-medium">Launching first in Toronto & Ottawa</span>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Barbers",
                services: ["• Classic cuts", "• Beard trims", "• Hot towel shaves", "• Hair styling"],
                href: "/services/barbers",
              },
              {
                title: "Makeup",
                services: ["• Event makeup", "• Bridal looks", "• Fashion makeup", "• Everyday glam"],
                href: "/services/makeup",
              },
              {
                title: "Salons",
                services: ["• Hair cuts & color", "• Blowouts", "• Treatments", "• Full styling"],
                href: "/services/salons",
              },
              {
                title: "Locs",
                services: ["• Loc maintenance", "• Retwisting", "• Loc styling", "• Loc repair"],
                href: "/services/locs",
              },
              {
                title: "Braids",
                services: ["• Box braids", "• Cornrows", "• French braids", "• Protective styles"],
                href: "/services/braids",
              },
              {
                title: "Nails",
                services: ["• Manicures", "• Pedicures", "• Nail art", "• Gel polish"],
                href: "/services/nails",
              },
              {
                title: "Brows",
                services: ["• Eyebrow shaping", "• Threading", "• Tinting", "• Microblading"],
                href: "/services/brows",
              },
              {
                title: "Eyelashes",
                services: ["• Lash extensions", "• Lash lifts", "• Lash tinting", "• Volume lashes"],
                href: "/services/eyelashes",
              },
            ].map((service, index) => (
              <div key={index}>
                <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl overflow-hidden h-full">
                  <CardContent className="p-6 text-center h-full flex flex-col justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>
                        {service.title}
                      </h3>
                      <div className="text-white/80 text-sm leading-relaxed drop-shadow-sm text-left">
                        {service.services.map((serviceItem, idx) => (
                          <div key={idx} className="mb-1">
                            {serviceItem}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-4">
                      <span className="text-xs px-3 py-1 rounded-full" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', color: '#F4F1E8'}}>
                        Coming Soon
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Providers Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>For Beauty Professionals</h2>
            <p className="text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-6">
              Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.
            </p>
            <Link
              href="/apply"
              className="inline-flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105"
              style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
            >
              Apply to Join Vierla
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {[
              {
                title: "Digital Store",
                description: "Create your own branded online presence with portfolio showcase, service listings, and customer reviews.",
                features: ["Custom branding", "Portfolio gallery", "Service catalog", "Customer reviews"]
              },
              {
                title: "Booking Management",
                description: "Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling.",
                features: ["Calendar sync", "Auto reminders", "Easy rescheduling", "Availability control"]
              },
              {
                title: "Business Analytics",
                description: "Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends.",
                features: ["Revenue tracking", "Customer insights", "Booking analytics", "Growth metrics"]
              },
              {
                title: "Payment Processing",
                description: "Secure payment handling with multiple payment methods and direct deposits to your preferred account.",
                features: ["Multiple payment methods", "Secure transactions", "Direct deposits", "Transaction history"]
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl overflow-hidden h-full flex flex-col"
              >
                <div className="p-8 flex flex-col h-full">
                  <div className="text-center flex-grow flex flex-col">
                    <h3 className="text-2xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>
                      {feature.title}
                    </h3>
                    <p className="text-white/80 leading-relaxed mb-6 drop-shadow-sm flex-grow">
                      {feature.description}
                    </p>
                    <div className="space-y-2 mt-auto">
                      {feature.features.map((item, idx) => (
                        <div key={idx} className="text-white/70 text-sm flex items-center justify-center">
                          <span className="w-2 h-2 rounded-full mr-2" style={{backgroundColor: '#F4F1E8'}}></span>
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto border border-white/30">
              <h3 className="text-4xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Coming Soon</h3>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-sm">
                We&apos;re building something special for beauty professionals. Join our waitlist to be the first to know when we launch.
              </p>

              {/* App Store Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                  <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Coming Soon to</div>
                    <div className="text-lg font-semibold text-white">Apple App Store</div>
                  </div>
                </div>

                <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                  <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                    <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                  </svg>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Coming Soon to</div>
                    <div className="text-lg font-semibold text-white">Google Play Store</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      <Footer variant="home" />

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>

      {/* A/B Test Results (Development Only) */}
      <ABTestResults />
    </div>
  )
}

