"use client"
import { useEffect } from 'react'
import { usePathname } from 'next/navigation'

// Analytics configuration
const ANALYTICS_CONFIG = {
  // Replace with your actual analytics IDs
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || 'G-XXXXXXXXXX',
  facebookPixelId: process.env.NEXT_PUBLIC_FB_PIXEL_ID || '000000000000000',
  hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID || '0000000',
  enabled: process.env.NODE_ENV === 'production'
}

// Google Analytics
export function GoogleAnalytics() {
  useEffect(() => {
    if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.googleAnalyticsId) return

    // Load Google Analytics
    const script1 = document.createElement('script')
    script1.async = true
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.googleAnalyticsId}`
    document.head.appendChild(script1)

    const script2 = document.createElement('script')
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${ANALYTICS_CONFIG.googleAnalyticsId}', {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {
          'custom_parameter_1': 'beauty_service_interest'
        }
      });
    `
    document.head.appendChild(script2)

    // Make gtag available globally
    ;(window as any).gtag = function() {
      ;(window as any).dataLayer.push(arguments)
    }

    return () => {
      document.head.removeChild(script1)
      document.head.removeChild(script2)
    }
  }, [])

  return null
}

// Facebook Pixel
export function FacebookPixel() {
  useEffect(() => {
    if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.facebookPixelId) return

    const script = document.createElement('script')
    script.innerHTML = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '${ANALYTICS_CONFIG.facebookPixelId}');
      fbq('track', 'PageView');
    `
    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return null
}

// Hotjar
export function Hotjar() {
  useEffect(() => {
    if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.hotjarId) return

    const script = document.createElement('script')
    script.innerHTML = `
      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:${ANALYTICS_CONFIG.hotjarId},hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    `
    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return null
}

// Page view tracking
export function usePageTracking() {
  const pathname = usePathname()

  useEffect(() => {
    if (!ANALYTICS_CONFIG.enabled) return

    const url = pathname

    // Google Analytics page view
    if ((window as any).gtag) {
      ;(window as any).gtag('config', ANALYTICS_CONFIG.googleAnalyticsId, {
        page_path: url,
        page_title: document.title
      })
    }

    // Facebook Pixel page view
    if ((window as any).fbq) {
      ;(window as any).fbq('track', 'PageView')
    }

    // Custom analytics storage
    const pageViews = JSON.parse(localStorage.getItem('vierla-page-views') || '[]')
    pageViews.push({
      url,
      title: document.title,
      timestamp: Date.now(),
      referrer: document.referrer,
      userAgent: navigator.userAgent
    })

    // Keep only last 100 page views
    if (pageViews.length > 100) {
      pageViews.splice(0, pageViews.length - 100)
    }

    localStorage.setItem('vierla-page-views', JSON.stringify(pageViews))
  }, [pathname])
}

// Event tracking utilities
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (!ANALYTICS_CONFIG.enabled) return

  // Google Analytics event
  if ((window as any).gtag) {
    ;(window as any).gtag('event', eventName, {
      event_category: 'engagement',
      event_label: parameters?.label || '',
      value: parameters?.value || 0,
      ...parameters
    })
  }

  // Facebook Pixel event
  if ((window as any).fbq) {
    ;(window as any).fbq('track', eventName, parameters)
  }

  // Custom event storage
  const events = JSON.parse(localStorage.getItem('vierla-events') || '[]')
  events.push({
    name: eventName,
    parameters,
    timestamp: Date.now(),
    url: window.location.pathname
  })

  // Keep only last 200 events
  if (events.length > 200) {
    events.splice(0, events.length - 200)
  }

  localStorage.setItem('vierla-events', JSON.stringify(events))
}

// Conversion tracking
export const trackConversion = (conversionType: string, value?: number) => {
  trackEvent('conversion', {
    conversion_type: conversionType,
    value: value || 0,
    currency: 'USD'
  })
}

// Email signup tracking
export const trackEmailSignup = (source: string) => {
  trackEvent('email_signup', {
    source,
    event_category: 'lead_generation'
  })
  
  trackConversion('email_signup')
}

// Contact form tracking
export const trackContactForm = (formType: string) => {
  trackEvent('contact_form_submit', {
    form_type: formType,
    event_category: 'lead_generation'
  })
  
  trackConversion('contact_form')
}

// Service interest tracking
export const trackServiceInterest = (service: string) => {
  trackEvent('service_interest', {
    service_type: service,
    event_category: 'engagement'
  })
}

// Professional interest tracking
export const trackProfessionalInterest = () => {
  trackEvent('professional_interest', {
    event_category: 'business_development'
  })
}

// Main Analytics component
export function Analytics() {
  usePageTracking()

  return (
    <>
      <GoogleAnalytics />
      <FacebookPixel />
      <Hotjar />
    </>
  )
}

// Utility to get analytics data (for debugging)
export function getAnalyticsData() {
  if (typeof window === 'undefined') return null

  return {
    pageViews: JSON.parse(localStorage.getItem('vierla-page-views') || '[]'),
    events: JSON.parse(localStorage.getItem('vierla-events') || '[]'),
    performance: JSON.parse(localStorage.getItem('vierla-performance') || '[]'),
    customMetrics: JSON.parse(localStorage.getItem('vierla-custom-metrics') || '[]')
  }
}
