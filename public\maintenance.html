<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Maintenance - Vierla</title>
    <meta name="theme-color" content="#364035">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        /* Reset and base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
            background-attachment: fixed;
            color: white;
            overflow-x: hidden;
        }

        /* Mobile fixes */
        @supports (-webkit-touch-callout: none) {
            html::before {
                content: '';
                position: fixed;
                top: -100px;
                left: -100px;
                right: -100px;
                bottom: -100px;
                background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
                z-index: -10;
                pointer-events: none;
            }
        }

        .container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .maintenance-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px 32px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: rgba(244, 241, 232, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            border: 2px solid rgba(244, 241, 232, 0.3);
        }

        .logo-text {
            font-size: 36px;
            font-weight: bold;
            color: #F4F1E8;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .maintenance-icon {
            width: 80px;
            height: 80px;
            background: rgba(184, 149, 106, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            border: 2px solid rgba(184, 149, 106, 0.3);
        }

        h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .subtitle {
            font-size: 24px;
            margin-bottom: 24px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 32px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: rgba(184, 149, 106, 0.1);
            border: 1px solid rgba(184, 149, 106, 0.3);
            border-radius: 12px;
            padding: 12px 20px;
            margin-bottom: 24px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #B8956A;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .contact-info {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .contact-info a {
            color: #B8956A;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-info a:hover {
            color: #F4F1E8;
            transition: color 0.3s ease;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .maintenance-card {
                padding: 32px 24px;
                margin: 16px;
            }

            h1 {
                font-size: 36px;
            }

            .subtitle {
                font-size: 20px;
            }

            .logo-text {
                font-size: 28px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
            }
        }

        /* Landscape orientation fix */
        @media screen and (orientation: landscape) and (max-height: 500px) {
            .container {
                padding: 10px;
            }
            
            .maintenance-card {
                padding: 24px 20px;
            }
            
            h1 {
                font-size: 28px;
                margin-bottom: 12px;
            }
            
            .subtitle {
                font-size: 18px;
                margin-bottom: 16px;
            }
            
            .maintenance-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="maintenance-card">
            <!-- Logo -->
            <div class="logo">
                <div class="logo-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F4F1E8" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                </div>
                <span class="logo-text">Vierla</span>
            </div>

            <!-- Maintenance Icon -->
            <div class="maintenance-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#B8956A" stroke-width="2">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
            </div>

            <!-- Content -->
            <h1>Under Maintenance</h1>
            <p class="subtitle">Your Self-Care, Simplified</p>
            
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Maintenance in Progress</span>
            </div>

            <p class="description">
                We're currently performing scheduled maintenance to improve your experience. 
                Our beauty services platform will be back online shortly with enhanced features and performance.
            </p>

            <p class="description">
                Thank you for your patience as we work to make Vierla even better for both customers and beauty professionals.
            </p>

            <!-- Contact Information -->
            <div class="contact-info">
                <p>Need immediate assistance?</p>
                <p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p style="margin-top: 16px; font-size: 14px;">
                    &copy; 2025 Vierla - Your Self-Care, Simplified. All rights reserved.
                </p>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh every 5 minutes to check if maintenance is complete
        setTimeout(function() {
            window.location.reload();
        }, 300000);
    </script>
</body>
</html>
