import { NextRequest, NextResponse } from 'next/server'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  type: string
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json()
    
    // Validate required fields
    const requiredFields = ['name', 'email', 'subject', 'message', 'type']
    const missingFields = requiredFields.filter(field => !body[field as keyof ContactFormData])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      )
    }

    // Validate contact type
    const validTypes = ['general', 'customer', 'professional', 'partnership', 'support']
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid contact type'
        },
        { status: 400 }
      )
    }

    // Log the contact form submission
    console.log('Contact form received:', {
      name: body.name,
      email: body.email,
      subject: body.subject,
      type: body.type,
      messageLength: body.message.length,
      timestamp: body.timestamp,
      ip: request.ip || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    // TODO: In a real implementation, you might:
    // 1. Save to database (PostgreSQL, MongoDB, etc.)
    // 2. Send email notification to admin team
    // 3. Send auto-reply confirmation to user
    // 4. Create ticket in support system (Zendesk, Freshdesk)
    // 5. Route to appropriate department based on type
    // 6. Integrate with CRM system
    // 7. Add to marketing automation if applicable

    return NextResponse.json({
      success: true,
      message: 'Contact form submitted successfully',
      data: {
        id: `contact_${Date.now()}`, // In real app, use proper ID generation
        timestamp: new Date().toISOString(),
        type: body.type
      }
    })

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
