version: '3.8'

services:
  vierla-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vierla-web
    restart: unless-stopped

    # Use host networking so Docker proxy & iptables forwarding issues are bypassed
    network_mode: host

    environment:
      - NODE_ENV=production
      - PORT=3000
      - APP_NAME=Vierla
      - APP_VERSION=1.0.0

    volumes:
      - ./logs:/app/logs

    healthcheck:
      test: ["CMD", "curl", "--silent", "--fail", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  logs:
    driver: local