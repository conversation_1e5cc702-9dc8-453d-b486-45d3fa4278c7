# 1. Base image for all stages
FROM node:18-alpine AS base
WORKDIR /app

# 2. Install deps
FROM base AS deps
RUN apk add --no-cache libc6-compat
COPY package.json package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm i --frozen-lockfile; \
  elif [ -f package-lock.json ]; then \
    npm ci --legacy-peer-deps; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 3. Build Next app
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
ENV NEXT_TELEMETRY_DISABLED=1
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm run build; \
  elif [ -f package-lock.json ]; then \
    npm run build; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 4. Production image
FROM node:18-alpine AS runner
WORKDIR /app

# 4a. Install curl (for healthcheck)
RUN apk add --no-cache curl

# 4b. Create a non-root user
RUN addgroup --system --gid 1001 nodejs \
 && adduser  --system --uid 1001 nextjs

# 4c. Copy static assets
COPY --from=builder /app/public ./public

# 4d. Copy the standalone build output (with its node_modules)
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone/ ./

# 4e. Copy static files for Next.js standalone mode
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 4f. Fix up permissions
RUN mkdir -p .next && chown nextjs:nodejs .next

# 4g. Switch to non-root
USER nextjs

# 4h. Tell Next.js to serve on all interfaces
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

EXPOSE 3000

# 4i. Healthcheck - use dedicated health endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 4j. Start the server
CMD ["node", "server.js", "--hostname", "0.0.0.0", "--port", "3000"]