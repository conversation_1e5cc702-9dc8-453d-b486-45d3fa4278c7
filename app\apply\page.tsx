"use client"
import { useState } from 'react'
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { trackEvent } from "@/components/analytics"
import { Footer } from "@/components/ui/footer"
import { submitProfessionalApplication } from "@/lib/api"

interface ApplicationData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  
  // Professional Information
  businessName: string
  services: string[]
  experience: string
  certifications: string[]
  
  // Location & Availability
  serviceAreas: string[]
  availability: string[]
  travelRadius: string
  
  // Business Details
  insurance: boolean
  license: string
  portfolio: string
  rates: string
  
  // Additional Information
  motivation: string
  references: string
}

const serviceOptions = [
  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',
  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',
  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',
  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',
  'Eyelash Extensions', 'Lash Lifts', '<PERSON>h <PERSON>',
  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',
  'Loc Maintenance', 'Loc Styling', 'Retwisting',
  'Beard Trimming', 'Hot Towel Shaves', 'Men\'s Grooming'
]

export default function ApplicationPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<ApplicationData>({
    firstName: '', lastName: '', email: '', phone: '',
    businessName: '', services: [], experience: '', certifications: [],
    serviceAreas: [], availability: [], travelRadius: '',
    insurance: false, license: '', portfolio: '', rates: '',
    motivation: '', references: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const totalSteps = 5

  const handleInputChange = (field: keyof ApplicationData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleArrayChange = (field: keyof ApplicationData, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...(prev[field] as string[]), value]
        : (prev[field] as string[]).filter(item => item !== value)
    }))
  }

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return !!(formData.firstName && formData.lastName && formData.email && formData.phone)
      case 2:
        return !!(formData.services.length > 0 && formData.experience)
      case 3:
        return !!(formData.license && formData.insurance !== undefined)
      case 4:
        return !!(formData.serviceAreas.length > 0 && formData.travelRadius && formData.rates)
      case 5:
        return !!(formData.motivation)
      default:
        return true
    }
  }

  const nextStep = () => {
    if (!validateCurrentStep()) {
      alert('Please fill in all required fields before proceeding.')
      return
    }

    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1)
      trackEvent('application_step_completed', {
        step: currentStep,
        event_category: 'professional_onboarding'
      })
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      // Submit to backend API with fallback to localStorage
      const response = await submitProfessionalApplication(formData)

      if (response.success) {
        setIsSubmitted(true)

        // Track application submission
        trackEvent('professional_application_submitted', {
          services_count: formData.services.length,
          experience_level: formData.experience,
          event_category: 'professional_onboarding'
        })
      } else {
        console.error('Application submission error:', response.error)
      }

    } catch (error) {
      console.error('Application submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Professional Background with Sophisticated Earth Tones */}
        <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)'}}>
          <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(54, 64, 53, 0.4), rgba(139, 154, 140, 0.3), rgba(54, 64, 53, 0.4))'}} />

          {/* Floating Blobs */}
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 300 + 100}px`,
                height: `${Math.random() * 300 + 100}px`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${Math.random() * 10 + 15}s`,
              }}
            />
          ))}
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20 text-center max-w-2xl">
            <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)'}}>
              <svg className="w-10 h-10" style={{color: '#F4F1E8'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>
              Application Submitted!
            </h1>
            
            <p className="text-white/90 text-lg mb-6">
              Thank you for your interest in joining Vierla. We've received your application and will review it within 3-5 business days.
            </p>
            
            <div className="bg-white/5 rounded-2xl p-6 mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">What happens next?</h3>
              <div className="space-y-3 text-left">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">1</span>
                  </div>
                  <p className="text-white/80">Application review and background check</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">2</span>
                  </div>
                  <p className="text-white/80">Video interview with our team</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center mt-0.5" style={{backgroundColor: '#B8956A'}}>
                    <span className="text-xs font-bold text-white">3</span>
                  </div>
                  <p className="text-white/80">Platform onboarding and training</p>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="px-6 py-3 rounded-xl border border-white/30 text-white hover:bg-white/10 transition-all duration-300"
              >
                Return Home
              </Link>
              <Link
                href="/about"
                className="px-6 py-3 rounded-xl font-medium transition-all duration-300"
                style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
              >
                Learn More About Vierla
              </Link>
            </div>
          </div>
        </div>

        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(2deg); }
            50% { transform: translateY(-40px) rotate(0deg); }
            75% { transform: translateY(-20px) rotate(-2deg); }
          }
          .animate-float {
            animation: float 20s ease-in-out infinite;
          }
        `}</style>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with Sophisticated Earth Tones */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(54, 64, 53, 0.4), rgba(139, 154, 140, 0.3), rgba(54, 64, 53, 0.4))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <HeartIcon className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
          </Link>
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
            <Link href="/about" className="text-white/90 hover:text-white transition-colors">About</Link>
          </div>
        </nav>
      </header>

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl md:text-3xl font-bold text-white" style={{fontFamily: 'Playfair Display, serif'}}>
                Join Vierla as a Professional
              </h1>
              <span className="text-white/80">Step {currentStep} of {totalSteps}</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className="h-2 rounded-full transition-all duration-300"
                style={{
                  backgroundColor: '#B8956A',
                  width: `${(currentStep / totalSteps) * 100}%`
                }}
              />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
            {/* Step Content */}
            {currentStep === 1 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Personal Information</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">First Name *</label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your first name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Last Name *</label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your last name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Email Address *</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Phone Number *</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="(*************"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Professional Information</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Business Name</label>
                    <input
                      type="text"
                      value={formData.businessName}
                      onChange={(e) => handleInputChange('businessName', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your business name (if applicable)"
                    />
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Services You Offer *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                      {serviceOptions.map((service) => (
                        <label key={service} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.services.includes(service)}
                            onChange={(e) => handleArrayChange('services', service, e.target.checked)}
                            className="rounded border-white/30 bg-white/10 text-white focus:ring-white/50"
                          />
                          <span className="text-white/90 text-sm">{service}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Years of Experience *</label>
                    <select
                      value={formData.experience}
                      onChange={(e) => handleInputChange('experience', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                      required
                    >
                      <option value="" className="bg-gray-800">Select experience level</option>
                      <option value="1-2" className="bg-gray-800">1-2 years</option>
                      <option value="3-5" className="bg-gray-800">3-5 years</option>
                      <option value="6-10" className="bg-gray-800">6-10 years</option>
                      <option value="10+" className="bg-gray-800">10+ years</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Licenses & Certifications</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional License Number *</label>
                    <input
                      type="text"
                      value={formData.license}
                      onChange={(e) => handleInputChange('license', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Your state license number"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional Insurance *</label>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="insurance"
                          checked={formData.insurance === true}
                          onChange={() => handleInputChange('insurance', true)}
                          className="text-white focus:ring-white/50"
                        />
                        <span className="text-white/90">Yes, I have professional liability insurance</span>
                      </label>
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="insurance"
                          checked={formData.insurance === false}
                          onChange={() => handleInputChange('insurance', false)}
                          className="text-white focus:ring-white/50"
                        />
                        <span className="text-white/90">No, I need help obtaining insurance</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Portfolio/Website URL</label>
                    <input
                      type="url"
                      value={formData.portfolio}
                      onChange={(e) => handleInputChange('portfolio', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="https://your-portfolio.com"
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Service Areas & Availability</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Service Areas *</label>
                    <textarea
                      value={formData.serviceAreas.join(', ')}
                      onChange={(e) => handleInputChange('serviceAreas', e.target.value.split(', '))}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="List cities/areas you serve (e.g., Manhattan, Brooklyn, Queens)"
                      rows={3}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Travel Radius *</label>
                    <select
                      value={formData.travelRadius}
                      onChange={(e) => handleInputChange('travelRadius', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                      required
                    >
                      <option value="" className="bg-gray-800">Select travel radius</option>
                      <option value="10" className="bg-gray-800">Within 10 miles</option>
                      <option value="25" className="bg-gray-800">Within 25 miles</option>
                      <option value="50" className="bg-gray-800">Within 50 miles</option>
                      <option value="unlimited" className="bg-gray-800">No limit</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Typical Rate Range *</label>
                    <input
                      type="text"
                      value={formData.rates}
                      onChange={(e) => handleInputChange('rates', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="e.g., $75-150 per service"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 5 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">Final Details</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Why do you want to join Vierla? *</label>
                    <textarea
                      value={formData.motivation}
                      onChange={(e) => handleInputChange('motivation', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="Tell us about your goals and what attracts you to our platform..."
                      rows={4}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2">Professional References</label>
                    <textarea
                      value={formData.references}
                      onChange={(e) => handleInputChange('references', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-md border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      placeholder="List 2-3 professional references with contact information (optional)"
                      rows={3}
                    />
                  </div>

                  <div className="bg-white/5 rounded-xl p-4">
                    <h3 className="text-lg font-semibold text-white mb-3">Application Review Process</h3>
                    <ul className="text-white/80 text-sm space-y-2">
                      <li>• Background check and license verification</li>
                      <li>• Portfolio and reference review</li>
                      <li>• Video interview with our team</li>
                      <li>• Platform training and onboarding</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <button
                onClick={prevStep}
                disabled={currentStep === 1}
                className="px-6 py-3 rounded-xl border border-white/30 text-white hover:bg-white/10 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {currentStep === totalSteps ? (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !validateCurrentStep()}
                  className="px-8 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
                  style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Application'}
                </button>
              ) : (
                <button
                  onClick={nextStep}
                  disabled={!validateCurrentStep()}
                  className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
                  style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
                >
                  Next
                </button>
              )}
            </div>
          </div>
        </div>
      </main>

      <Footer />

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
