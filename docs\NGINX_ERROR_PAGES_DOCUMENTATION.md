# 🚨 NGINX Error Pages & Maintenance Mode Documentation

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Target**: Vierla.com Production Environment  

---

## 📋 **Overview**

This documentation covers the implementation of custom error pages and maintenance mode for the Vierla website. The system provides branded error pages that maintain the website's visual identity and user experience even during errors or maintenance periods.

---

## 🎨 **Custom Error Pages**

### **Available Error Pages**
1. **404 - Page Not Found** (`/public/error-404.html`)
2. **500 - Server Error** (`/public/error-500.html`)
3. **Maintenance Mode** (`/public/maintenance.html`)

### **Design Features**
- **Consistent Branding**: Matches Vierla's color scheme and typography
- **Mobile Responsive**: Optimized for all device sizes
- **Accessibility**: WCAG 2.1 compliant with proper contrast ratios
- **Professional Appearance**: Maintains trust even during errors
- **Clear Actions**: Provides helpful navigation options

---

## 🔧 **NGINX Configuration**

### **Configuration File Location**
```
/etc/nginx/sites-available/vierla.com
```

### **Include Error Pages Configuration**
Add the following include directive to your main server block:

```nginx
server {
    listen 443 ssl http2;
    server_name vierla.com www.vierla.com;
    
    # Include Vierla error pages configuration
    include /etc/nginx/conf.d/vierla-error-pages.conf;
    
    # Your existing configuration...
}
```

### **Error Page Configuration Details**

#### **Basic Error Page Setup**
```nginx
# Error page mappings
error_page 404 /error-404.html;
error_page 500 502 503 504 /error-500.html;

# Error page locations
location = /error-404.html {
    internal;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

location = /error-500.html {
    internal;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
```

#### **Maintenance Mode Configuration**
```nginx
# Maintenance mode check
location / {
    if (-f $document_root/maintenance.flag) {
        return 503;
    }
    try_files $uri $uri/ @nextjs;
}

# Maintenance page handler
error_page 503 @maintenance;
location @maintenance {
    rewrite ^(.*)$ /maintenance.html break;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Retry-After 3600;
}
```

---

## 🛠️ **Installation Instructions**

### **Step 1: Copy Error Page Files**
```bash
# Copy error page files to web root
sudo cp public/error-404.html /var/www/vierla.com/public/
sudo cp public/error-500.html /var/www/vierla.com/public/
sudo cp public/maintenance.html /var/www/vierla.com/public/

# Set proper permissions
sudo chown www-data:www-data /var/www/vierla.com/public/*.html
sudo chmod 644 /var/www/vierla.com/public/*.html
```

### **Step 2: Install NGINX Configuration**
```bash
# Copy NGINX configuration
sudo cp nginx/vierla-error-pages.conf /etc/nginx/conf.d/

# Test NGINX configuration
sudo nginx -t

# Reload NGINX if test passes
sudo systemctl reload nginx
```

### **Step 3: Install Maintenance Script**
```bash
# Copy maintenance script
sudo cp scripts/maintenance.sh /usr/local/bin/vierla-maintenance
sudo chmod +x /usr/local/bin/vierla-maintenance

# Create log directory
sudo mkdir -p /var/log
sudo touch /var/log/vierla-maintenance.log
sudo chown www-data:www-data /var/log/vierla-maintenance.log
```

---

## 🔄 **Maintenance Mode Management**

### **Enable Maintenance Mode**
```bash
# Enable maintenance mode
sudo vierla-maintenance enable

# Enable for specific duration (30 minutes)
sudo vierla-maintenance schedule 30
```

### **Disable Maintenance Mode**
```bash
# Disable maintenance mode
sudo vierla-maintenance disable
```

### **Check Status**
```bash
# Check current status
sudo vierla-maintenance status
```

### **Manual Maintenance Mode**
```bash
# Manual enable (create flag file)
sudo touch /var/www/vierla.com/maintenance.flag
sudo systemctl reload nginx

# Manual disable (remove flag file)
sudo rm /var/www/vierla.com/maintenance.flag
sudo systemctl reload nginx
```

---

## 📊 **Monitoring & Logging**

### **Log Files**
- **NGINX Access Log**: `/var/log/nginx/vierla-access.log`
- **NGINX Error Log**: `/var/log/nginx/vierla-error.log`
- **Maintenance Log**: `/var/log/vierla-maintenance.log`

### **Monitoring Commands**
```bash
# Monitor NGINX error log
sudo tail -f /var/log/nginx/vierla-error.log

# Monitor maintenance log
sudo tail -f /var/log/vierla-maintenance.log

# Check error page access
sudo grep "error-" /var/log/nginx/vierla-access.log
```

### **Health Check Script**
```bash
#!/bin/bash
# Check if error pages are accessible
curl -I https://vierla.com/nonexistent-page 2>/dev/null | grep "404"
curl -I https://vierla.com/error-500.html 2>/dev/null | grep "200"
```

---

## 🔒 **Security Considerations**

### **File Permissions**
```bash
# Ensure proper permissions
sudo chown -R www-data:www-data /var/www/vierla.com/public/
sudo chmod 644 /var/www/vierla.com/public/*.html
sudo chmod 600 /var/www/vierla.com/maintenance.flag
```

### **Access Control**
```nginx
# Prevent direct access to maintenance flag
location = /maintenance.flag {
    deny all;
    return 404;
}

# Security headers for error pages
location ~* \.(html)$ {
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

---

## 🧪 **Testing Procedures**

### **Test Error Pages**
```bash
# Test 404 error page
curl -I https://vierla.com/nonexistent-page

# Test 500 error page (simulate server error)
# Temporarily stop the Next.js application
sudo systemctl stop vierla-app
curl -I https://vierla.com
sudo systemctl start vierla-app
```

### **Test Maintenance Mode**
```bash
# Enable maintenance mode
sudo vierla-maintenance enable

# Test maintenance page
curl -I https://vierla.com
# Should return HTTP 503

# Test maintenance page content
curl https://vierla.com | grep "Under Maintenance"

# Disable maintenance mode
sudo vierla-maintenance disable

# Verify normal operation
curl -I https://vierla.com
# Should return HTTP 200
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Error Pages Not Showing**
```bash
# Check file permissions
ls -la /var/www/vierla.com/public/*.html

# Check NGINX configuration
sudo nginx -t

# Check NGINX error log
sudo tail -20 /var/log/nginx/vierla-error.log
```

#### **Maintenance Mode Not Working**
```bash
# Check if flag file exists
ls -la /var/www/vierla.com/maintenance.flag

# Check NGINX configuration
sudo nginx -t

# Reload NGINX configuration
sudo systemctl reload nginx
```

#### **Permission Denied Errors**
```bash
# Fix file permissions
sudo chown www-data:www-data /var/www/vierla.com/public/*.html
sudo chmod 644 /var/www/vierla.com/public/*.html

# Check SELinux context (if applicable)
sudo restorecon -R /var/www/vierla.com/public/
```

---

## 📋 **Maintenance Checklist**

### **Pre-Maintenance**
- [ ] Notify users about scheduled maintenance
- [ ] Backup current website files
- [ ] Test maintenance page accessibility
- [ ] Verify maintenance script functionality

### **During Maintenance**
- [ ] Enable maintenance mode
- [ ] Verify maintenance page is showing
- [ ] Monitor logs for any issues
- [ ] Perform required maintenance tasks

### **Post-Maintenance**
- [ ] Disable maintenance mode
- [ ] Verify website is functioning normally
- [ ] Check all critical pages
- [ ] Monitor error logs for issues

---

## 📞 **Emergency Procedures**

### **Emergency Maintenance Enable**
```bash
# Quick enable (if script fails)
sudo touch /var/www/vierla.com/maintenance.flag
sudo systemctl reload nginx
```

### **Emergency Maintenance Disable**
```bash
# Quick disable (if script fails)
sudo rm -f /var/www/vierla.com/maintenance.flag
sudo systemctl reload nginx
```

### **Rollback Procedures**
```bash
# Restore original NGINX configuration
sudo cp /etc/nginx/sites-available/vierla.com.backup /etc/nginx/sites-available/vierla.com
sudo nginx -t && sudo systemctl reload nginx

# Remove error page configuration
sudo rm /etc/nginx/conf.d/vierla-error-pages.conf
sudo systemctl reload nginx
```

---

## 📈 **Performance Impact**

### **Minimal Performance Impact**
- Error pages are static HTML files
- No database queries or server-side processing
- Cached with appropriate headers
- Lightweight file sizes (< 50KB each)

### **Maintenance Mode Impact**
- Immediate response with 503 status
- No backend processing during maintenance
- Automatic retry headers for clients
- Minimal server resource usage

---

## 🔄 **Updates and Maintenance**

### **Updating Error Pages**
```bash
# Update error page files
sudo cp new-error-404.html /var/www/vierla.com/public/error-404.html
sudo chown www-data:www-data /var/www/vierla.com/public/error-404.html

# No NGINX reload required for content updates
```

### **Configuration Updates**
```bash
# Update NGINX configuration
sudo nano /etc/nginx/conf.d/vierla-error-pages.conf
sudo nginx -t && sudo systemctl reload nginx
```

---

## 📞 **Support Information**

### **Contact Information**
- **Technical Support**: <EMAIL>
- **Emergency Contact**: Follow escalation procedures

### **Documentation Updates**
- Update this documentation when making configuration changes
- Test all procedures after updates
- Maintain version control for configuration files

---

**This documentation ensures reliable error handling and maintenance procedures for the Vierla platform while maintaining brand consistency and user experience.**
