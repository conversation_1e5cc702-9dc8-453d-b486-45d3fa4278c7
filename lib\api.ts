// API configuration and utilities for backend integration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'

// API response types
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Email signup data
interface EmailSignupData {
  email: string
  source: string
  timestamp: string
}

// Contact form data
interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  type: string
  timestamp: string
}

// Professional application data
interface ProfessionalApplicationData {
  firstName: string
  lastName: string
  email: string
  phone: string
  businessName: string
  services: string[]
  experience: string
  certifications: string[]
  serviceAreas: string[]
  availability: string[]
  travelRadius: string
  insurance: boolean
  license: string
  portfolio: string
  rates: string
  motivation: string
  references: string
  timestamp: string
}

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('API request failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

// Email signup API
export async function submitEmailSignup(email: string, source: string): Promise<ApiResponse> {
  const data: EmailSignupData = {
    email,
    source,
    timestamp: new Date().toISOString()
  }

  // Fallback to localStorage if API fails
  try {
    const response = await apiRequest('/email-signup', {
      method: 'POST',
      body: JSON.stringify(data)
    })

    if (!response.success) {
      throw new Error(response.error)
    }

    return response
  } catch (error) {
    // Fallback to localStorage
    const existingEmails = JSON.parse(localStorage.getItem('vierla-signups') || '[]')
    if (!existingEmails.find((item: EmailSignupData) => item.email === email)) {
      existingEmails.push(data)
      localStorage.setItem('vierla-signups', JSON.stringify(existingEmails))
    }

    return {
      success: true,
      message: 'Email saved locally (backend unavailable)'
    }
  }
}

// Contact form API
export async function submitContactForm(formData: Omit<ContactFormData, 'timestamp'>): Promise<ApiResponse> {
  const data: ContactFormData = {
    ...formData,
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiRequest('/contact', {
      method: 'POST',
      body: JSON.stringify(data)
    })

    if (!response.success) {
      throw new Error(response.error)
    }

    return response
  } catch (error) {
    // Fallback to localStorage
    const existingContacts = JSON.parse(localStorage.getItem('vierla-contacts') || '[]')
    existingContacts.push(data)
    localStorage.setItem('vierla-contacts', JSON.stringify(existingContacts))

    return {
      success: true,
      message: 'Contact form saved locally (backend unavailable)'
    }
  }
}

// Professional application API
export async function submitProfessionalApplication(
  formData: Omit<ProfessionalApplicationData, 'timestamp'>
): Promise<ApiResponse> {
  const data: ProfessionalApplicationData = {
    ...formData,
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiRequest('/professional-application', {
      method: 'POST',
      body: JSON.stringify(data)
    })

    if (!response.success) {
      throw new Error(response.error)
    }

    return response
  } catch (error) {
    // Fallback to localStorage
    const existingApplications = JSON.parse(localStorage.getItem('vierla-applications') || '[]')
    existingApplications.push({
      ...data,
      status: 'pending'
    })
    localStorage.setItem('vierla-applications', JSON.stringify(existingApplications))

    return {
      success: true,
      message: 'Application saved locally (backend unavailable)'
    }
  }
}

// Analytics API
export async function submitAnalyticsEvent(
  eventName: string,
  eventData: Record<string, any>
): Promise<ApiResponse> {
  const data = {
    event: eventName,
    data: eventData,
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.pathname : '',
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : ''
  }

  try {
    const response = await apiRequest('/analytics', {
      method: 'POST',
      body: JSON.stringify(data)
    })

    return response
  } catch (error) {
    // Fallback to localStorage
    const existingEvents = JSON.parse(localStorage.getItem('vierla-events') || '[]')
    existingEvents.push(data)
    
    // Keep only last 200 events
    if (existingEvents.length > 200) {
      existingEvents.splice(0, existingEvents.length - 200)
    }
    
    localStorage.setItem('vierla-events', JSON.stringify(existingEvents))

    return {
      success: true,
      message: 'Event saved locally (backend unavailable)'
    }
  }
}

// Health check API
export async function checkBackendHealth(): Promise<boolean> {
  try {
    const response = await apiRequest('/health')
    return response.success
  } catch (error) {
    return false
  }
}

// Get backend status
export async function getBackendStatus(): Promise<{
  available: boolean
  version?: string
  uptime?: number
}> {
  try {
    const response = await apiRequest('/status')
    return {
      available: true,
      version: response.data?.version,
      uptime: response.data?.uptime
    }
  } catch (error) {
    return { available: false }
  }
}

// Export all data for backend sync
export function exportLocalData() {
  return {
    emailSignups: JSON.parse(localStorage.getItem('vierla-signups') || '[]'),
    contacts: JSON.parse(localStorage.getItem('vierla-contacts') || '[]'),
    applications: JSON.parse(localStorage.getItem('vierla-applications') || '[]'),
    events: JSON.parse(localStorage.getItem('vierla-events') || '[]'),
    pageViews: JSON.parse(localStorage.getItem('vierla-page-views') || '[]'),
    performance: JSON.parse(localStorage.getItem('vierla-performance') || '[]'),
    customMetrics: JSON.parse(localStorage.getItem('vierla-custom-metrics') || '[]')
  }
}

// Clear local data after successful sync
export function clearLocalData() {
  localStorage.removeItem('vierla-signups')
  localStorage.removeItem('vierla-contacts')
  localStorage.removeItem('vierla-applications')
  localStorage.removeItem('vierla-events')
  localStorage.removeItem('vierla-page-views')
  localStorage.removeItem('vierla-performance')
  localStorage.removeItem('vierla-custom-metrics')
}
