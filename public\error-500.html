<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Server <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON></title>
    <meta name="theme-color" content="#364035">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        /* Reset and base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
            background-attachment: fixed;
            color: white;
            overflow-x: hidden;
        }

        /* Mobile fixes */
        @supports (-webkit-touch-callout: none) {
            html::before {
                content: '';
                position: fixed;
                top: -100px;
                left: -100px;
                right: -100px;
                bottom: -100px;
                background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
                z-index: -10;
                pointer-events: none;
            }
        }

        .container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px 32px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: rgba(244, 241, 232, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            border: 2px solid rgba(244, 241, 232, 0.3);
        }

        .logo-text {
            font-size: 36px;
            font-weight: bold;
            color: #F4F1E8;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .error-icon {
            width: 80px;
            height: 80px;
            background: rgba(220, 38, 38, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            border: 2px solid rgba(220, 38, 38, 0.3);
        }

        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #dc2626;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 16px;
            font-family: 'Georgia', serif;
        }

        h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-family: 'Georgia', serif;
        }

        .description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 32px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 32px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: #B8956A;
            color: #2D2A26;
        }

        .btn-primary:hover {
            background: #A67C52;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #F4F1E8;
            border-color: rgba(244, 241, 232, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(244, 241, 232, 0.1);
            transform: translateY(-2px);
        }

        .contact-info {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .contact-info a {
            color: #B8956A;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-info a:hover {
            color: #F4F1E8;
            transition: color 0.3s ease;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .error-card {
                padding: 32px 24px;
                margin: 16px;
            }

            .error-code {
                font-size: 80px;
            }

            h1 {
                font-size: 36px;
            }

            .logo-text {
                font-size: 28px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-card">
            <!-- Logo -->
            <div class="logo">
                <div class="logo-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F4F1E8" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                </div>
                <span class="logo-text">Vierla</span>
            </div>

            <!-- Error Icon -->
            <div class="error-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#dc2626" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
            </div>

            <!-- Error Code -->
            <div class="error-code">500</div>

            <!-- Content -->
            <h1>Server Error</h1>
            
            <p class="description">
                We're experiencing technical difficulties on our end. 
                Our team has been notified and is working to resolve the issue as quickly as possible.
            </p>

            <p class="description">
                Please try again in a few minutes. We apologize for any inconvenience.
            </p>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="/" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                    </svg>
                    Go Home
                </a>
                <a href="javascript:window.location.reload()" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                        <polyline points="23 4 23 10 17 10"></polyline>
                        <polyline points="1 20 1 14 7 14"></polyline>
                        <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                    </svg>
                    Try Again
                </a>
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <p>If the problem persists, please contact us:</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p style="margin-top: 16px; font-size: 14px;">
                    &copy; 2025 Vierla - Your Self-Care, Simplified. All rights reserved.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
